import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_core/firebase_core.dart';
import '../models/realtime_notification_model.dart';
import '../models/realtime_bug_report_model.dart';

class RealtimeDatabaseService {
  final FirebaseDatabase _database = FirebaseDatabase.instanceFor(
    app: Firebase.app(),
    databaseURL:
        'https://moviefinder-98-default-rtdb.asia-southeast1.firebasedatabase.app/',
  );

  // References
  late final DatabaseReference _notificationsRef;
  late final DatabaseReference _userNotificationsRef;
  late final DatabaseReference _bugReportsRef;
  late final DatabaseReference _notificationSettingsRef;
  late final DatabaseReference _notificationStatsRef;

  // Stream controllers for real-time updates
  final Map<String, StreamSubscription> _activeStreams = {};

  RealtimeDatabaseService() {
    _notificationsRef = _database.ref('notifications');
    _userNotificationsRef = _database.ref('user_notifications');
    _bugReportsRef = _database.ref('bug_reports');
    _notificationSettingsRef = _database.ref('notification_settings');
    _notificationStatsRef = _database.ref('notification_stats');
  }

  // Cleanup method to dispose streams
  void dispose() {
    for (var subscription in _activeStreams.values) {
      subscription.cancel();
    }
    _activeStreams.clear();
  }

  // ==================== HELPER METHODS ====================

  // Lấy danh sách ID của admin và developer
  Future<List<String>> _getAdminAndDeveloperIds() async {
    try {
      // Tạm thời return empty list - có thể implement sau
      // Hoặc hardcode admin/dev IDs nếu biết trước
      return [
        'AyhGknRFQqNq5xI96XBA1j246S83', // Admin ID hiện tại
        // Thêm các admin/dev IDs khác nếu có
      ];
    } catch (e) {
      print('Error getting admin/developer IDs: $e');
      return [];
    }
  }

  // ==================== NOTIFICATIONS ====================

  // Lấy tất cả thông báo công khai
  Stream<List<RealtimeNotificationModel>> getPublicNotificationsStream() {
    final query = _notificationsRef
        .orderByChild('isPublic')
        .equalTo(true)
        .limitToLast(50);

    return query.onValue.map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      return data.entries.map((entry) {
        return RealtimeNotificationModel.fromJson(
          entry.key.toString(),
          Map<String, dynamic>.from(entry.value as Map),
        );
      }).toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    });
  }

  // Lấy thông báo cho người dùng cụ thể với trạng thái
  Stream<List<NotificationViewModel>> getUserNotificationsStream(
      String userId) {
    // Lấy danh sách ID thông báo của người dùng
    return _userNotificationsRef
        .orderByChild('userId')
        .equalTo(userId)
        .onValue
        .asyncMap((event) async {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return <NotificationViewModel>[];

      // Lọc các thông báo chưa bị xóa
      final userNotifications = data.entries
          .map((entry) => RealtimeUserNotificationModel.fromJson(
                entry.key.toString(),
                Map<String, dynamic>.from(entry.value as Map),
              ))
          .where((notification) => !notification.isDeleted)
          .toList();

      // Lấy chi tiết thông báo và combine với user state
      final List<NotificationViewModel> notifications = [];
      for (var userNotification in userNotifications) {
        final snapshot = await _notificationsRef
            .child(userNotification.notificationId)
            .get();

        if (snapshot.exists) {
          final notificationData = snapshot.value as Map<dynamic, dynamic>;
          final notification = RealtimeNotificationModel.fromJson(
            snapshot.key!,
            Map<String, dynamic>.from(notificationData),
          );

          notifications.add(NotificationViewModel(
            notification: notification,
            userNotification: userNotification,
          ));
        }
      }

      return notifications
        ..sort((a, b) =>
            b.notification.createdAt.compareTo(a.notification.createdAt));
    });
  }

  // Tạo thông báo mới
  Future<String?> createNotification({
    required String title,
    required String body,
    String? imageUrl,
    String? targetScreen,
    Map<String, dynamic>? data,
    bool isPublic = false,
    List<String>? targetUserIds,
    String type = 'system',
    String priority = 'normal',
    Duration? expiresIn,
  }) async {
    try {
      final now = DateTime.now();
      final expiresAt = now.add(expiresIn ?? const Duration(days: 30));

      final newNotificationRef = _notificationsRef.push();
      final notificationId = newNotificationRef.key;

      if (notificationId == null) return null;

      final notification = RealtimeNotificationModel(
        id: notificationId,
        title: title,
        body: body,
        imageUrl: imageUrl,
        createdAt: now.millisecondsSinceEpoch,
        expiresAt: expiresAt.millisecondsSinceEpoch,
        targetScreen: targetScreen,
        data: data,
        isPublic: isPublic,
        targetUserIds: targetUserIds,
        type: type,
        priority: priority,
      );

      await newNotificationRef.set(notification.toJson());

      // Nếu có người dùng cụ thể, tạo bản ghi user_notification
      if (targetUserIds != null && targetUserIds.isNotEmpty) {
        for (var userId in targetUserIds) {
          await _userNotificationsRef.push().set({
            'userId': userId,
            'notificationId': notificationId,
            'isRead': false,
            'isSeen': false,
            'isDeleted': false,
            'createdAt': now.millisecondsSinceEpoch,
          });
        }
      }

      return notificationId;
    } catch (e) {
      print('Error creating notification: $e');
      return null;
    }
  }

  // Đánh dấu thông báo đã đọc cho người dùng cụ thể
  Future<bool> markNotificationAsRead(
      String userId, String notificationId) async {
    try {
      // Tìm user notification record trong root level
      final snapshot = await _userNotificationsRef
          .orderByChild('userId')
          .equalTo(userId)
          .once();

      if (snapshot.snapshot.value != null) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        for (var entry in data.entries) {
          final userNotification =
              Map<String, dynamic>.from(entry.value as Map);
          if (userNotification['notificationId'] == notificationId) {
            await _userNotificationsRef.child(entry.key).update({
              'isRead': true,
              'readAt': DateTime.now().millisecondsSinceEpoch,
            });
            return true;
          }
        }
      }

      // Nếu chưa có record, tạo mới
      await _userNotificationsRef.push().set({
        'userId': userId,
        'notificationId': notificationId,
        'isRead': true,
        'isSeen': true,
        'isDeleted': false,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
        'readAt': DateTime.now().millisecondsSinceEpoch,
        'seenAt': DateTime.now().millisecondsSinceEpoch,
      });

      return true;
    } catch (e) {
      print('Error marking notification as read: $e');
      return false;
    }
  }

  // Đánh dấu thông báo đã xem
  Future<bool> markNotificationAsSeen(
      String userId, String notificationId) async {
    try {
      // Tìm user notification record trong root level
      final snapshot = await _userNotificationsRef
          .orderByChild('userId')
          .equalTo(userId)
          .once();

      if (snapshot.snapshot.value != null) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        for (var entry in data.entries) {
          final userNotification =
              Map<String, dynamic>.from(entry.value as Map);
          if (userNotification['notificationId'] == notificationId) {
            await _userNotificationsRef.child(entry.key).update({
              'isSeen': true,
              'seenAt': DateTime.now().millisecondsSinceEpoch,
            });
            return true;
          }
        }
      }

      // Nếu chưa có record, tạo mới
      await _userNotificationsRef.push().set({
        'userId': userId,
        'notificationId': notificationId,
        'isRead': false,
        'isSeen': true,
        'isDeleted': false,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
        'seenAt': DateTime.now().millisecondsSinceEpoch,
      });

      return true;
    } catch (e) {
      print('Error marking notification as seen: $e');
      return false;
    }
  }

  // Xóa thông báo (soft delete)
  Future<bool> deleteNotification(String userId, String notificationId) async {
    try {
      // Tìm user notification record trong root level
      final snapshot = await _userNotificationsRef
          .orderByChild('userId')
          .equalTo(userId)
          .once();

      if (snapshot.snapshot.value != null) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        for (var entry in data.entries) {
          final userNotification =
              Map<String, dynamic>.from(entry.value as Map);
          if (userNotification['notificationId'] == notificationId) {
            await _userNotificationsRef.child(entry.key).update({
              'isDeleted': true,
              'deletedAt': DateTime.now().millisecondsSinceEpoch,
            });
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      print('Error deleting notification: $e');
      return false;
    }
  }

  // Đánh dấu tất cả thông báo đã đọc
  Future<bool> markAllNotificationsAsRead(String userId) async {
    try {
      final snapshot = await _userNotificationsRef
          .orderByChild('userId')
          .equalTo(userId)
          .once();

      if (snapshot.snapshot.value != null) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        final now = DateTime.now().millisecondsSinceEpoch;

        for (var entry in data.entries) {
          final userNotification =
              Map<String, dynamic>.from(entry.value as Map);
          if (!userNotification['isRead'] && !userNotification['isDeleted']) {
            await _userNotificationsRef.child(entry.key).update({
              'isRead': true,
              'readAt': now,
            });
          }
        }
      }

      return true;
    } catch (e) {
      print('Error marking all notifications as read: $e');
      return false;
    }
  }

  // Đánh dấu tất cả thông báo đã xem
  Future<bool> markAllNotificationsAsSeen(String userId) async {
    try {
      final snapshot = await _userNotificationsRef
          .orderByChild('userId')
          .equalTo(userId)
          .once();

      if (snapshot.snapshot.value != null) {
        final data = snapshot.snapshot.value as Map<dynamic, dynamic>;
        final now = DateTime.now().millisecondsSinceEpoch;

        for (var entry in data.entries) {
          final userNotification =
              Map<String, dynamic>.from(entry.value as Map);
          if (!userNotification['isSeen'] && !userNotification['isDeleted']) {
            await _userNotificationsRef.child(entry.key).update({
              'isSeen': true,
              'seenAt': now,
            });
          }
        }
      }

      return true;
    } catch (e) {
      print('Error marking all notifications as seen: $e');
      return false;
    }
  }

  // ==================== BUG REPORTS ====================

  // Lấy tất cả báo cáo lỗi
  Stream<List<RealtimeBugReportModel>> getAllBugReportsStream() {
    return _bugReportsRef.orderByChild('createdAt').onValue.map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      return data.entries.map((entry) {
        return RealtimeBugReportModel.fromJson(
          entry.key.toString(),
          Map<String, dynamic>.from(entry.value as Map),
        );
      }).toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    });
  }

  // Lấy báo cáo lỗi theo trạng thái
  Stream<List<RealtimeBugReportModel>> getBugReportsByStatusStream(
      String status) {
    return _bugReportsRef
        .orderByChild('status')
        .equalTo(status)
        .onValue
        .map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      return data.entries.map((entry) {
        return RealtimeBugReportModel.fromJson(
          entry.key.toString(),
          Map<String, dynamic>.from(entry.value as Map),
        );
      }).toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    });
  }

  // Lấy báo cáo lỗi của người dùng
  Stream<List<RealtimeBugReportModel>> getUserBugReportsStream(String userId) {
    return _bugReportsRef
        .orderByChild('reportedBy')
        .equalTo(userId)
        .onValue
        .map((event) {
      final data = event.snapshot.value as Map<dynamic, dynamic>?;
      if (data == null) return [];

      return data.entries.map((entry) {
        return RealtimeBugReportModel.fromJson(
          entry.key.toString(),
          Map<String, dynamic>.from(entry.value as Map),
        );
      }).toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    });
  }

  // Lấy chi tiết báo cáo lỗi theo ID
  Future<RealtimeBugReportModel?> getBugReportById(String bugReportId) async {
    try {
      final snapshot = await _bugReportsRef.child(bugReportId).get();

      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        return RealtimeBugReportModel.fromJson(
          snapshot.key!,
          Map<String, dynamic>.from(data),
        );
      }

      return null;
    } catch (e) {
      print('Error getting bug report by ID: $e');
      return null;
    }
  }

  // Lấy chi tiết báo cáo lỗi theo thời gian thực
  Stream<RealtimeBugReportModel?> getBugReportStream(String bugReportId) {
    return _bugReportsRef.child(bugReportId).onValue.map((event) {
      if (event.snapshot.exists && event.snapshot.value != null) {
        final data = event.snapshot.value as Map<dynamic, dynamic>;
        return RealtimeBugReportModel.fromJson(
          bugReportId,
          Map<String, dynamic>.from(data),
        );
      }
      return null;
    });
  }

  // Tạo báo cáo lỗi mới
  Future<String?> createBugReport({
    required String title,
    required String description,
    required String reportedBy,
    required String reportedByName,
    String? reportedByEmail,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final now = DateTime.now();

      final newBugReportRef = _bugReportsRef.push();
      final bugReportId = newBugReportRef.key;

      if (bugReportId == null) return null;

      // Tạo báo cáo lỗi
      await newBugReportRef.set({
        'title': title,
        'description': description,
        'reportedBy': reportedBy,
        'reportedByName': reportedByName,
        'reportedByEmail': reportedByEmail,
        'createdAt': now.millisecondsSinceEpoch,
        'status': 'pending',
        'additionalData': additionalData,
      });

      // Thêm phản hồi tự động
      final responseId = DateTime.now().millisecondsSinceEpoch.toString();
      await newBugReportRef.child('responses').child(responseId).set({
        'responderId': 'system',
        'responderName': 'Hệ thống',
        'message':
            'Cảm ơn bạn đã báo cáo lỗi. Vui lòng theo dõi báo cáo lỗi để nhận thông tin cập nhật từ đội ngũ phát triển.',
        'createdAt': now.millisecondsSinceEpoch,
        'isFromAdmin': false,
        'isFromDeveloper': false,
      });

      // Tạo thông báo cho người dùng - chỉ thông báo cảm ơn
      await createNotification(
        title: 'Cảm ơn bạn đã báo lỗi',
        body:
            'Cảm ơn bạn đã báo cáo lỗi. Vui lòng theo dõi báo cáo lỗi để nhận thông tin cập nhật từ đội ngũ phát triển.',
        targetScreen: 'bug_report_detail',
        data: {'bugReportId': bugReportId},
        isPublic: false,
        targetUserIds: [reportedBy],
        type: 'bug_report_user',
      );

      // Thông báo cho admin và developer - CHỈ admin/dev nhận được
      await createNotification(
        title: 'Báo cáo lỗi mới',
        body: 'Người dùng $reportedByName đã báo cáo lỗi: $title',
        targetScreen: 'bug_report_detail',
        data: {'bugReportId': bugReportId, 'isAdminNotification': 'true'},
        isPublic: false, // Đổi thành false để không phải public
        targetUserIds:
            await _getAdminAndDeveloperIds(), // Chỉ gửi cho admin/dev
        type: 'bug_report_admin',
      );

      return bugReportId;
    } catch (e) {
      print('Error creating bug report: $e');
      return null;
    }
  }

  // Thêm phản hồi cho báo cáo lỗi
  Future<bool> addBugResponse({
    required String bugReportId,
    required String responderId,
    required String responderName,
    required String message,
    bool isFromAdmin = false,
    bool isFromDeveloper = false,
    String? newStatus,
  }) async {
    try {
      final now = DateTime.now();
      final responseId = now.millisecondsSinceEpoch.toString();

      // Thêm phản hồi
      await _bugReportsRef
          .child(bugReportId)
          .child('responses')
          .child(responseId)
          .set({
        'responderId': responderId,
        'responderName': responderName,
        'message': message,
        'createdAt': now.millisecondsSinceEpoch,
        'isFromAdmin': isFromAdmin,
        'isFromDeveloper': isFromDeveloper,
        'newStatus': newStatus,
      });

      // Cập nhật trạng thái nếu có
      if (newStatus != null) {
        await _bugReportsRef.child(bugReportId).update({
          'status': newStatus,
        });
      }

      // Lấy thông tin người báo cáo
      final snapshot = await _bugReportsRef.child(bugReportId).get();
      if (snapshot.exists) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        final reportedBy = data['reportedBy'] as String?;
        final title = data['title'] as String? ?? 'Báo cáo lỗi';

        // Gửi thông báo cho người báo cáo
        if (reportedBy != null && (isFromAdmin || isFromDeveloper)) {
          await createNotification(
            title: 'Phản hồi cho báo cáo lỗi của bạn',
            body: message,
            targetScreen: 'bug_report_detail',
            data: {'bugReportId': bugReportId},
            isPublic: false,
            targetUserIds: [reportedBy],
          );
        }

        // Nếu developer thay đổi trạng thái, thông báo cho admin
        if (isFromDeveloper && newStatus != null) {
          await createNotification(
            title: 'Cập nhật trạng thái báo cáo lỗi',
            body:
                '$responderName đã cập nhật trạng thái báo cáo lỗi "$title" thành ${_getStatusDisplayName(newStatus)}',
            targetScreen: 'bug_report_detail',
            data: {'bugReportId': bugReportId, 'isAdminNotification': 'true'},
            isPublic: false, // Đổi thành false
            targetUserIds:
                await _getAdminAndDeveloperIds(), // Chỉ gửi cho admin/dev
            type: 'bug_report_status',
          );
        }
      }

      return true;
    } catch (e) {
      print('Error adding bug response: $e');
      return false;
    }
  }

  // Cập nhật trạng thái báo cáo lỗi
  Future<bool> updateBugStatus({
    required String bugReportId,
    required String newStatus,
    required String updatedBy,
    required String updatedByName,
    bool isFromDeveloper = false,
  }) async {
    try {
      // Cập nhật trạng thái
      await _bugReportsRef.child(bugReportId).update({
        'status': newStatus,
      });

      // Thêm phản hồi về việc thay đổi trạng thái
      await addBugResponse(
        bugReportId: bugReportId,
        responderId: updatedBy,
        responderName: updatedByName,
        message:
            'Đã cập nhật trạng thái thành: ${_getStatusDisplayName(newStatus)}',
        isFromAdmin: !isFromDeveloper,
        isFromDeveloper: isFromDeveloper,
        newStatus: newStatus,
      );

      return true;
    } catch (e) {
      print('Error updating bug status: $e');
      return false;
    }
  }

  // Cập nhật trạng thái cho phép phản hồi của người dùng
  Future<bool> updateAllowUserResponse({
    required String bugReportId,
    required bool allowUserResponse,
    required String updatedBy,
    required String updatedByName,
    bool isFromDeveloper = false,
  }) async {
    try {
      // Cập nhật trạng thái cho phép phản hồi
      await _bugReportsRef.child(bugReportId).update({
        'allowUserResponse': allowUserResponse,
      });

      // Thêm phản hồi về việc thay đổi trạng thái
      final message = allowUserResponse
          ? 'Đã cho phép người dùng phản hồi'
          : 'Đã tắt phản hồi của người dùng';

      await addBugResponse(
        bugReportId: bugReportId,
        responderId: updatedBy,
        responderName: updatedByName,
        message: message,
        isFromAdmin: !isFromDeveloper,
        isFromDeveloper: isFromDeveloper,
      );

      return true;
    } catch (e) {
      print('Error updating allow user response: $e');
      return false;
    }
  }

  // Hàm hỗ trợ lấy tên hiển thị của trạng thái
  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'accepted':
        return 'Đã nhận';
      case 'inProgress':
        return 'Đang fix';
      case 'fixed':
        return 'Đã fix';
      default:
        return 'Chưa nhận';
    }
  }

  // ==================== NOTIFICATION SETTINGS ====================

  // Lấy cài đặt thông báo của người dùng
  Future<Map<String, dynamic>?> getUserNotificationSettings(
      String userId) async {
    try {
      final snapshot = await _notificationSettingsRef.child(userId).get();
      if (snapshot.exists) {
        return Map<String, dynamic>.from(snapshot.value as Map);
      }
      return null;
    } catch (e) {
      print('Error getting user notification settings: $e');
      return null;
    }
  }

  // Cập nhật cài đặt thông báo của người dùng
  Future<bool> updateUserNotificationSettings(
    String userId,
    Map<String, dynamic> settings,
  ) async {
    try {
      await _notificationSettingsRef.child(userId).set(settings);
      return true;
    } catch (e) {
      print('Error updating user notification settings: $e');
      return false;
    }
  }

  // ==================== NOTIFICATION STATISTICS ====================

  // Cập nhật thống kê thông báo
  Future<void> updateNotificationStats({
    required String type,
    required String action, // 'created', 'read', 'deleted'
  }) async {
    try {
      final now = DateTime.now();
      final dateKey =
          '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

      await _notificationStatsRef
          .child(dateKey)
          .child(type)
          .child(action)
          .runTransaction((currentValue) {
        final current = (currentValue as int?) ?? 0;
        return Transaction.success(current + 1);
      });
    } catch (e) {
      print('Error updating notification stats: $e');
    }
  }

  // Lấy thống kê thông báo
  Future<Map<String, dynamic>?> getNotificationStats(String dateKey) async {
    try {
      final snapshot = await _notificationStatsRef.child(dateKey).get();
      if (snapshot.exists) {
        return Map<String, dynamic>.from(snapshot.value as Map);
      }
      return null;
    } catch (e) {
      print('Error getting notification stats: $e');
      return null;
    }
  }

  // ==================== ENHANCED NOTIFICATION METHODS ====================

  // Tạo thông báo với tính năng nâng cao
  Future<String?> createEnhancedNotification({
    required String title,
    required String body,
    String? imageUrl,
    String? targetScreen,
    Map<String, dynamic>? data,
    bool isPublic = false,
    List<String>? targetUserIds,
    String type = 'system',
    String priority = 'normal',
    Duration? expiresIn,
    DateTime? scheduledAt,
    List<String>? tags,
    Map<String, dynamic>? actionButtons,
  }) async {
    try {
      final now = DateTime.now();
      final expiresAt = now.add(expiresIn ?? const Duration(days: 30));
      final isScheduled = scheduledAt != null && scheduledAt.isAfter(now);

      final newNotificationRef = _notificationsRef.push();
      final notificationId = newNotificationRef.key;

      if (notificationId == null) return null;

      final notification = {
        'id': notificationId,
        'title': title,
        'body': body,
        'imageUrl': imageUrl,
        'createdAt': now.millisecondsSinceEpoch,
        'expiresAt': expiresAt.millisecondsSinceEpoch,
        'targetScreen': targetScreen,
        'data': data,
        'isPublic': isPublic,
        'targetUserIds': targetUserIds,
        'type': type,
        'priority': priority,
        'isScheduled': isScheduled,
        'scheduledAt': scheduledAt?.millisecondsSinceEpoch,
        'tags': tags,
        'actionButtons': actionButtons,
        'status': isScheduled ? 'scheduled' : 'active',
      };

      await newNotificationRef.set(notification);

      // Cập nhật thống kê
      await updateNotificationStats(type: type, action: 'created');

      // Nếu không phải thông báo được lên lịch, tạo user_notification ngay
      if (!isScheduled && targetUserIds != null && targetUserIds.isNotEmpty) {
        for (var userId in targetUserIds) {
          await _userNotificationsRef.push().set({
            'userId': userId,
            'notificationId': notificationId,
            'isRead': false,
            'isSeen': false,
            'isDeleted': false,
            'createdAt': now.millisecondsSinceEpoch,
          });
        }
      }

      return notificationId;
    } catch (e) {
      print('Error creating enhanced notification: $e');
      return null;
    }
  }

  // Xử lý thông báo được lên lịch
  Future<void> processScheduledNotifications() async {
    try {
      final now = DateTime.now();
      final query =
          _notificationsRef.orderByChild('status').equalTo('scheduled');

      final snapshot = await query.get();
      if (!snapshot.exists) return;

      final notifications = Map<String, dynamic>.from(snapshot.value as Map);

      for (var entry in notifications.entries) {
        final notificationData = Map<String, dynamic>.from(entry.value);
        final scheduledAt = notificationData['scheduledAt'] as int?;

        if (scheduledAt != null &&
            DateTime.fromMillisecondsSinceEpoch(scheduledAt).isBefore(now)) {
          // Kích hoạt thông báo
          await _notificationsRef.child(entry.key).update({
            'status': 'active',
            'activatedAt': now.millisecondsSinceEpoch,
          });

          // Tạo user_notification cho các user được chỉ định
          final targetUserIds =
              notificationData['targetUserIds'] as List<dynamic>?;
          if (targetUserIds != null && targetUserIds.isNotEmpty) {
            for (var userId in targetUserIds) {
              await _userNotificationsRef.push().set({
                'userId': userId.toString(),
                'notificationId': entry.key,
                'isRead': false,
                'isSeen': false,
                'isDeleted': false,
                'createdAt': now.millisecondsSinceEpoch,
              });
            }
          }
        }
      }
    } catch (e) {
      print('Error processing scheduled notifications: $e');
    }
  }

  // Dọn dẹp thông báo hết hạn
  Future<void> cleanupExpiredNotifications() async {
    try {
      final now = DateTime.now();
      final query = _notificationsRef
          .orderByChild('expiresAt')
          .endAt(now.millisecondsSinceEpoch);

      final snapshot = await query.get();
      if (!snapshot.exists) return;

      final expiredNotifications =
          Map<String, dynamic>.from(snapshot.value as Map);

      for (var notificationId in expiredNotifications.keys) {
        // Xóa thông báo
        await _notificationsRef.child(notificationId).remove();

        // Xóa user_notifications liên quan
        final userNotificationsQuery = _userNotificationsRef
            .orderByChild('notificationId')
            .equalTo(notificationId);

        final userNotificationsSnapshot = await userNotificationsQuery.get();
        if (userNotificationsSnapshot.exists) {
          final userNotifications =
              Map<String, dynamic>.from(userNotificationsSnapshot.value as Map);

          for (var userNotificationId in userNotifications.keys) {
            await _userNotificationsRef.child(userNotificationId).remove();
          }
        }
      }
    } catch (e) {
      print('Error cleaning up expired notifications: $e');
    }
  }
}
